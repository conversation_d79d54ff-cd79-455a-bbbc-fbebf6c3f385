import { Routes } from '@angular/router';
import { permissionGuard } from 'app/core/auth/guards/permission.guard';

import { UsersComponent } from './users/users.component';

export default [
    {
        path: 'users',
        component: UsersComponent,
        canActivate: [permissionGuard],
        data: { permission: "ADMIN_USERS" },
    },
    {
        path: 'role',
        canActivate: [permissionGuard],
        data: { permission: "ADMIN_ROLE_MASTER" },
        loadChildren: () => import('./role/role.routes')
    },
    {
        path: 'brand',
        canActivate: [permissionGuard],
        data: { permission: "ADMIN_BRAND_MASTER" },
        loadChildren: () => import('./brand/brand.routes')
    },
    {
        path: 'category',
        canActivate: [permissionGuard],
        data: { permission: "ADMIN_CATEGORY_MASTER" },
        loadChildren: () => import('./category/category.routes')
    },
    {
        path: 'auditor',
        canActivate: [permissionGuard],
        data: { permission: "ADMIN_AUDITOR_MASTER" },
        loadChildren: () => import('./auditor/auditor.routes')
    },
      {
        path: 'technician',
        canActivate: [permissionGuard],
        data: { permission: "ADMIN_TECHNICIAN_MASTER" },
        loadChildren: () => import('./category/category.routes')
    },
    { path: 'not-found', loadChildren: () => import('app/modules/page-not-found/page-not-found.routes') },
    { path: '**', pathMatch: 'full', redirectTo: 'not-found' },
] as Routes;
